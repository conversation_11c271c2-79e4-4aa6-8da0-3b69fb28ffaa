{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"typeRoots": ["node_modules/@types", "src/types"], "types": ["jest"], "baseUrl": "./src/", "paths": {"@assets/*": ["assets/*"], "@features/*": ["features/*"], "@service/*": ["service/*"], "@styles/*": ["styles/*"], "@navigation/*": ["navigation/*"], "@components/*": ["components/*"], "@state/*": ["state/*"], "@utils/*": ["utils/*"]}}}